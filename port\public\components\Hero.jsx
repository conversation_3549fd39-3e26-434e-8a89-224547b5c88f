import { motion } from "framer-motion";
import { FaG<PERSON><PERSON>, Fa<PERSON><PERSON>edin, Fa<PERSON>out<PERSON>, FaTwitter } from "react-icons/fa";

export default function Hero() {
  return (
    <section className="min-h-screen flex items-center justify-center bg-[#0c0c0f] text-white px-6">
      <div className="container mx-auto flex flex-col md:flex-row items-center justify-between gap-10">
        
        {/* Left Content */}
        <div className="flex flex-col max-w-xl">
          <h3 className="text-gray-400 mb-2">Software Developer</h3>
          <h1 className="text-5xl font-bold leading-tight mb-4">
            Hello I’m <br />
            <span className="text-green-400"><PERSON></span>
          </h1>
          <p className="text-gray-400 mb-6">
            I excel at crafting elegant digital experiences and I am proficient
            in various programming languages and technologies.
          </p>

          {/* Buttons */}
          <div className="flex items-center gap-4 mb-8">
            <button className="px-6 py-2 border border-green-400 rounded-full text-green-400 hover:bg-green-400 hover:text-black transition">
              DOWNLOAD CV ⬇
            </button>
            <div className="flex gap-4 text-xl">
              <FaGithub className="cursor-pointer hover:text-green-400" />
              <FaLinkedin className="cursor-pointer hover:text-green-400" />
              <FaYoutube className="cursor-pointer hover:text-green-400" />
              <FaTwitter className="cursor-pointer hover:text-green-400" />
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-6 text-center">
            <div>
              <p className="text-3xl font-bold">12</p>
              <p className="text-gray-400 text-sm">Years of experience</p>
            </div>
            <div>
              <p className="text-3xl font-bold">26</p>
              <p className="text-gray-400 text-sm">Projects completed</p>
            </div>
            <div>
              <p className="text-3xl font-bold">8</p>
              <p className="text-gray-400 text-sm">Technologies mastered</p>
            </div>
            <div>
              <p className="text-3xl font-bold">499</p>
              <p className="text-gray-400 text-sm">Code commits</p>
            </div>
          </div>
        </div>

        {/* Right Image */}
        <motion.div
          className="relative w-64 h-64 rounded-full overflow-hidden border-2 border-green-400 flex items-center justify-center"
          animate={{ rotate: [0, 360] }}
          transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
        >
          <img
            src="/profile.png" // Replace with your own image
            alt="Profile"
            className="w-full h-full object-cover"
          />
        </motion.div>
      </div>
    </section>
  );
}
