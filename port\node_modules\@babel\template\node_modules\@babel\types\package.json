{"name": "@babel/types", "version": "7.0.0-beta.56", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-types", "main": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {"esutils": "^2.0.2", "lodash": "^4.17.10", "to-fast-properties": "^2.0.0"}, "devDependencies": {"@babel/generator": "7.0.0-beta.56", "@babel/parser": "7.0.0-beta.56"}}