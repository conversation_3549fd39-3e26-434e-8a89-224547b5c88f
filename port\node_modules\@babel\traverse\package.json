{"name": "@babel/traverse", "version": "7.0.0-beta.56", "description": "The Babel Traverse module maintains the overall tree state, and is responsible for replacing, removing, and adding nodes", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-traverse", "main": "lib/index.js", "dependencies": {"@babel/code-frame": "7.0.0-beta.56", "@babel/generator": "7.0.0-beta.56", "@babel/helper-function-name": "7.0.0-beta.56", "@babel/helper-split-export-declaration": "7.0.0-beta.56", "@babel/parser": "7.0.0-beta.56", "@babel/types": "7.0.0-beta.56", "debug": "^3.1.0", "globals": "^11.1.0", "lodash": "^4.17.10"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.56"}}