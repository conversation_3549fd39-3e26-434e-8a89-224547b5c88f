import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import { Canvas } from '@react-three/fiber'
import { Model } from './components/model/model'
import { OrbitControls } from '@react-three/drei'



function App() {
  const [count, setCount] = useState(0)

  return (
    <>
      <Canvas
        style={{
          height:'100vh',
          width:'100vw'

        }}
      >
          <OrbitControls></OrbitControls>
          <ambientLight></ambientLight>
          <Model position={[3, 0, 0]} />

      </Canvas>
    </>
  )
}

export default App
