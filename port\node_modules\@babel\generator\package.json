{"name": "@babel/generator", "version": "7.0.0-beta.56", "description": "Turns an AST into code.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-generator", "main": "lib/index.js", "files": ["lib"], "dependencies": {"@babel/types": "7.0.0-beta.56", "jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1"}, "devDependencies": {"@babel/helper-fixtures": "7.0.0-beta.56", "@babel/parser": "7.0.0-beta.56"}}