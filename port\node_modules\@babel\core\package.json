{"name": "@babel/core", "version": "7.0.0-beta.56", "description": "Babel compiler core.", "main": "lib/index.js", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-core", "keywords": ["6to5", "babel", "classes", "const", "es6", "harmony", "let", "modules", "transpile", "transpiler", "var", "babel-core", "compiler"], "engines": {"node": ">=6.9.0"}, "browser": {"./lib/config/files/index.js": "./lib/config/files/index-browser.js", "./lib/transform-file.js": "./lib/transform-file-browser.js"}, "dependencies": {"@babel/code-frame": "7.0.0-beta.56", "@babel/generator": "7.0.0-beta.56", "@babel/helpers": "7.0.0-beta.56", "@babel/parser": "7.0.0-beta.56", "@babel/template": "7.0.0-beta.56", "@babel/traverse": "7.0.0-beta.56", "@babel/types": "7.0.0-beta.56", "convert-source-map": "^1.1.0", "debug": "^3.1.0", "json5": "^0.5.0", "lodash": "^4.17.10", "resolve": "^1.3.2", "semver": "^5.4.1", "source-map": "^0.5.0"}, "devDependencies": {"@babel/helper-transform-fixture-test-runner": "7.0.0-beta.56", "@babel/register": "7.0.0-beta.56"}}