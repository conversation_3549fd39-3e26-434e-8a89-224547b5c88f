import { useEffect, useState } from 'react'
import './App.css'
import  DOTS from 'vanta/dist/vanta.dots.min'
import Navbar from './components/Navbar';


function App() {
  useEffect(()=>{
    DOTS({
      el:'#vanta',
      mouseControls: true,
      touchControls: true,
      gyroControls: false,
      minHeight: 200.00,
      minWidth: 200.00,
      scale: 1.00,
      scaleMobile: 1.00,
      color: 0x0,
      color2: 0x0,
      backgroundColor: 0xffffff,
      showLines: false
    })
  
  },[])
  
  return (
    <>
     <div className="app">
        <Navbar/>
        <div className="bg" id="vanta"></div>
        
        
        
     </div>
    </>
  )
}

export default App
