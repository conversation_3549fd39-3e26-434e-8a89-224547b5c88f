import { useEffect, useState } from 'react'
import './App.css'
import  TRUNK from 'vanta/dist/vanta.trunk.min'
import Navbar from './components/Navbar';


function App() {
  useEffect(()=>{
    TRUNK({
      el:'#vanta',
      mouseControls: true,
      touchControls: true,
      gyroControls: false,
      minHeight: 200.00,
      minWidth: 200.00,
      scale: 1.00,
      scaleMobile: 1.00,
      color: 0xffffff,
      chaos: 1.50
    })
  
  },[])
  
  return (
    <>
     <div className="app">
        
        <div className="bg" id="vanta"></div>
        <div className='navbar'><Navbar/></div>
        
        
        
     </div>
    </>
  )
}

export default App
