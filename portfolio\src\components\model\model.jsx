import React, { useRef, useMemo } from 'react'
import { useGLTF } from '@react-three/drei'
import * as THREE from 'three'

export function Model(props) {
  const { nodes } = useGLTF('/need_some_space/scene.gltf')

  // Create a circular texture for round particles
  const circleTexture = useMemo(() => {
    const canvas = document.createElement('canvas')
    canvas.width = 64
    canvas.height = 64
    const context = canvas.getContext('2d')

    const gradient = context.createRadialGradient(32, 32, 0, 32, 32, 32)
    gradient.addColorStop(0, 'rgba(255,255,255,1)')
    gradient.addColorStop(0.2, 'rgba(255,255,255,1)')
    gradient.addColorStop(0.4, 'rgba(255,255,255,0.8)')
    gradient.addColorStop(1, 'rgba(255,255,255,0)')

    context.fillStyle = gradient
    context.fillRect(0, 0, 64, 64)

    return new THREE.CanvasTexture(canvas)
  }, [])

  return (
    <group {...props} dispose={null}>
      <points
        geometry={nodes.Object_2.geometry}
        rotation={[-Math.PI / 2, 0, 0]}
        scale={0.013}
      >
        <pointsMaterial
          color="#4a90e2"
          size={2}
          sizeAttenuation={false}
          transparent={true}
          alphaTest={0.001}
          map={circleTexture}
        />
      </points>
    </group>
  )
}

useGLTF.preload('/need_some_space/scene.gltf')
