{"name": "@babel/template", "version": "7.0.0-beta.56", "description": "Generate an AST from a string template.", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "repository": "https://github.com/babel/babel/tree/master/packages/babel-template", "main": "lib/index.js", "dependencies": {"@babel/code-frame": "7.0.0-beta.56", "@babel/parser": "7.0.0-beta.56", "@babel/types": "7.0.0-beta.56", "lodash": "^4.17.10"}}