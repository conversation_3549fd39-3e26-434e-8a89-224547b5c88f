{"name": "@babel/parser", "version": "7.0.0-beta.56", "description": "A JavaScript parser", "author": "<PERSON> <<EMAIL>>", "homepage": "https://babeljs.io/", "license": "MIT", "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "repository": "https://github.com/babel/babel/tree/master/packages/babel-parser", "main": "lib/index.js", "files": ["bin", "lib"], "engines": {"node": ">=6.0.0"}, "devDependencies": {"@babel/helper-fixtures": "7.0.0-beta.56", "charcodes": "0.1.0", "unicode-11.0.0": "^0.7.7"}, "bin": {"parser": "./bin/babel-parser.js"}, "publishConfig": {"tag": "next"}}